package com.superhexa.supervision.feature.miwear.speechhub.presentation.recording

import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.superhexa.supervision.library.db.bean.AudioTranscriptionBean
import com.superhexa.supervision.library.db.bean.MediaBean
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import timber.log.Timber

/**
 * 录音列表性能优化测试
 * 验证优化后的性能表现，确保：
 * 1. 只有状态变化的条目会重新渲染
 * 2. 批量更新能正确工作
 * 3. 独立状态流能减少不必要的重组
 */
@RunWith(AndroidJUnit4::class)
class RecordingListPerformanceTest {

    @get:Rule
    val composeTestRule = createComposeRule()

    private lateinit var mockViewModel: RecordingListViewModel
    private lateinit var testAudioList: List<AudioTranscriptionBean>
    private lateinit var testMediaList: List<MediaBean>

    @Before
    fun setup() {
        mockViewModel = mockk(relaxed = true)
        
        // 创建测试数据
        testAudioList = createTestAudioList()
        testMediaList = createTestMediaList()
        
        // 设置 mock 行为
        every { mockViewModel.audioMutableList } returns MutableStateFlow(testAudioList)
        every { mockViewModel.mutableList } returns mutableStateOf(testMediaList.toMutableList())
    }

    @Test
    fun `测试批量状态更新性能`() = runTest {
        // 准备状态更新数据
        val statusUpdates = mapOf(
            "/path/audio1.wav" to "TRANSCRIBING",
            "/path/audio2.wav" to "TRANSCRIPTION_COMPLETED",
            "/path/audio3.wav" to "UPLOAD_FAILED"
        )

        // 执行批量更新
        mockViewModel.batchUpdateProcessStatus(statusUpdates)

        // 验证只调用了一次批量更新，而不是多次单独更新
        verify(exactly = 1) { mockViewModel.batchUpdateProcessStatus(statusUpdates) }
        
        Timber.d("批量状态更新测试通过")
    }

    @Test
    fun `测试独立状态流创建和订阅`() = runTest {
        val testFilePath = "/path/audio1.wav"
        val testStatus = "TRANSCRIBING"

        // 模拟获取独立状态流
        val statusFlow = MutableStateFlow<String?>(null)
        every { mockViewModel.getProcessStatusFlow(testFilePath) } returns statusFlow

        // 更新状态
        statusFlow.value = testStatus

        // 验证状态流被正确创建和更新
        verify { mockViewModel.getProcessStatusFlow(testFilePath) }
        
        Timber.d("独立状态流测试通过")
    }

    @Test
    fun `测试状态变化检测逻辑`() {
        val fragment = NormalRecordingListFragment()
        
        // 测试状态无变化的情况
        val noChangeResult = fragment.shouldUpdateProcessStatus("TRANSCRIBING", "TRANSCRIBING")
        assert(!noChangeResult) { "相同状态不应该触发更新" }
        
        // 测试状态有变化的情况
        val hasChangeResult = fragment.shouldUpdateProcessStatus("UPLOADING", "TRANSCRIBING")
        assert(hasChangeResult) { "不同状态应该触发更新" }
        
        // 测试从 null 到有状态的情况
        val fromNullResult = fragment.shouldUpdateProcessStatus(null, "TRANSCRIBING")
        assert(fromNullResult) { "从 null 到有状态应该触发更新" }
        
        Timber.d("状态变化检测逻辑测试通过")
    }

    @Test
    fun `测试RecordItem组件重组优化`() {
        composeTestRule.setContent {
            val testItem = testMediaList.first()
            val testAudioItem = testAudioList.first()
            
            // 模拟独立状态流
            val processStatus by remember(testItem.path) {
                MutableStateFlow<String?>("TRANSCRIBING")
            }.collectAsState()

            // 验证组件能正确使用独立状态流
            assert(processStatus == "TRANSCRIBING") { "状态应该正确传递" }
        }
        
        Timber.d("RecordItem组件重组优化测试通过")
    }

    private fun createTestAudioList(): List<AudioTranscriptionBean> {
        return listOf(
            AudioTranscriptionBean(
                id = 1,
                path = "/path/audio1.wav",
                transcriptionStatus = "TRANSCRIBING",
                summaryStatus = "NONE"
            ),
            AudioTranscriptionBean(
                id = 2,
                path = "/path/audio2.wav",
                transcriptionStatus = "SUCCESS",
                summaryStatus = "NONE"
            ),
            AudioTranscriptionBean(
                id = 3,
                path = "/path/audio3.wav",
                transcriptionStatus = "UPLOAD_FAILED",
                summaryStatus = "NONE"
            )
        )
    }

    private fun createTestMediaList(): List<MediaBean> {
        return listOf(
            MediaBean().apply {
                id = 1
                path = "/path/audio1.wav"
                fileName = "录音1"
                duration = 60000L
            },
            MediaBean().apply {
                id = 2
                path = "/path/audio2.wav"
                fileName = "录音2"
                duration = 120000L
            },
            MediaBean().apply {
                id = 3
                path = "/path/audio3.wav"
                fileName = "录音3"
                duration = 90000L
            }
        )
    }
}

/**
 * 扩展函数用于测试私有方法
 */
private fun NormalRecordingListFragment.shouldUpdateProcessStatus(
    currentStatus: String?,
    newStatus: String
): Boolean {
    // 通过反射调用私有方法进行测试
    val method = this::class.java.getDeclaredMethod(
        "shouldUpdateProcessStatus",
        String::class.java,
        String::class.java
    )
    method.isAccessible = true
    return method.invoke(this, currentStatus, newStatus) as Boolean
}
