package com.superhexa.supervision.feature.miwear.speechhub.presentation.recording

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import timber.log.Timber
import kotlin.system.measureTimeMillis

/**
 * 性能监控工具
 * 用于监控录音列表的性能表现，包括：
 * 1. 重组次数统计
 * 2. 状态更新耗时
 * 3. 列表渲染性能
 */
object PerformanceMonitor {
    
    private var recompositionCount = 0
    private var statusUpdateCount = 0
    private var batchUpdateCount = 0
    private var lastUpdateTime = 0L
    
    /**
     * 记录重组事件
     */
    fun recordRecomposition(componentName: String) {
        recompositionCount++
        Timber.d("性能监控: $componentName 重组，总重组次数: $recompositionCount")
    }
    
    /**
     * 记录状态更新事件
     */
    fun recordStatusUpdate(filePath: String, oldStatus: String?, newStatus: String) {
        statusUpdateCount++
        val currentTime = System.currentTimeMillis()
        val timeSinceLastUpdate = if (lastUpdateTime > 0) currentTime - lastUpdateTime else 0
        lastUpdateTime = currentTime
        
        Timber.i("性能监控: 状态更新 - 文件: $filePath, $oldStatus -> $newStatus, " +
                "距离上次更新: ${timeSinceLastUpdate}ms, 总更新次数: $statusUpdateCount")
    }
    
    /**
     * 记录批量更新事件
     */
    fun recordBatchUpdate(updateCount: Int, duration: Long) {
        batchUpdateCount++
        Timber.i("性能监控: 批量更新 - 更新条目数: $updateCount, 耗时: ${duration}ms, " +
                "总批量更新次数: $batchUpdateCount")
    }
    
    /**
     * 测量操作耗时
     */
    inline fun <T> measureOperation(operationName: String, operation: () -> T): T {
        val result: T
        val duration = measureTimeMillis {
            result = operation()
        }
        Timber.d("性能监控: $operationName 耗时: ${duration}ms")
        return result
    }
    
    /**
     * 获取性能统计信息
     */
    fun getPerformanceStats(): PerformanceStats {
        return PerformanceStats(
            recompositionCount = recompositionCount,
            statusUpdateCount = statusUpdateCount,
            batchUpdateCount = batchUpdateCount,
            lastUpdateTime = lastUpdateTime
        )
    }
    
    /**
     * 重置性能统计
     */
    fun resetStats() {
        recompositionCount = 0
        statusUpdateCount = 0
        batchUpdateCount = 0
        lastUpdateTime = 0L
        Timber.d("性能监控: 统计数据已重置")
    }
    
    /**
     * 打印性能报告
     */
    fun printPerformanceReport() {
        val stats = getPerformanceStats()
        Timber.i("""
            ========== 录音列表性能报告 ==========
            总重组次数: ${stats.recompositionCount}
            状态更新次数: ${stats.statusUpdateCount}
            批量更新次数: ${stats.batchUpdateCount}
            最后更新时间: ${stats.lastUpdateTime}
            平均每次批量更新的状态数: ${if (stats.batchUpdateCount > 0) stats.statusUpdateCount / stats.batchUpdateCount else 0}
            =====================================
        """.trimIndent())
    }
}

/**
 * 性能统计数据类
 */
data class PerformanceStats(
    val recompositionCount: Int,
    val statusUpdateCount: Int,
    val batchUpdateCount: Int,
    val lastUpdateTime: Long
)

/**
 * 性能监控 Composable
 * 用于在 Compose 组件中监控重组性能
 */
@Composable
fun PerformanceTracker(
    componentName: String,
    content: @Composable () -> Unit
) {
    var recompositionCount by remember { mutableStateOf(0) }
    
    LaunchedEffect(Unit) {
        recompositionCount++
        PerformanceMonitor.recordRecomposition("$componentName-$recompositionCount")
    }
    
    content()
}

/**
 * 状态更新性能监控装饰器
 */
class PerformanceAwareViewModel(private val delegate: RecordingListViewModel) {
    
    fun updateProcessStatusWithMonitoring(filePath: String, status: String) {
        val oldStatus = delegate.getProcessStatus(filePath)
        
        PerformanceMonitor.measureOperation("单个状态更新") {
            delegate.updateProcessStatus(filePath, status)
        }
        
        PerformanceMonitor.recordStatusUpdate(filePath, oldStatus, status)
    }
    
    fun batchUpdateProcessStatusWithMonitoring(statusUpdates: Map<String, String>) {
        val duration = measureTimeMillis {
            delegate.batchUpdateProcessStatus(statusUpdates)
        }
        
        PerformanceMonitor.recordBatchUpdate(statusUpdates.size, duration)
    }
}

/**
 * 列表滚动性能监控
 */
object ScrollPerformanceMonitor {
    
    private var scrollEvents = 0
    private var lastScrollTime = 0L
    
    fun recordScrollEvent() {
        scrollEvents++
        val currentTime = System.currentTimeMillis()
        val timeSinceLastScroll = if (lastScrollTime > 0) currentTime - lastScrollTime else 0
        lastScrollTime = currentTime
        
        if (timeSinceLastScroll < 16) { // 低于 60fps
            Timber.w("性能警告: 滚动性能可能存在问题，距离上次滚动仅 ${timeSinceLastScroll}ms")
        }
        
        Timber.d("滚动性能监控: 滚动事件 #$scrollEvents, 距离上次滚动: ${timeSinceLastScroll}ms")
    }
    
    fun getScrollStats(): ScrollStats {
        return ScrollStats(
            scrollEvents = scrollEvents,
            lastScrollTime = lastScrollTime
        )
    }
    
    fun resetScrollStats() {
        scrollEvents = 0
        lastScrollTime = 0L
    }
}

/**
 * 滚动统计数据类
 */
data class ScrollStats(
    val scrollEvents: Int,
    val lastScrollTime: Long
)
