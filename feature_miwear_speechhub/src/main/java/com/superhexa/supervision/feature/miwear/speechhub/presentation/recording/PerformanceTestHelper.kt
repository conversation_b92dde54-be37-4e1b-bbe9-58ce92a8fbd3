package com.superhexa.supervision.feature.miwear.speechhub.presentation.recording

import com.superhexa.supervision.library.db.bean.AudioTranscriptionBean
import kotlinx.coroutines.delay
import timber.log.Timber

/**
 * 性能测试辅助工具
 * 用于模拟转写状态变化，测试优化后的性能表现
 */
object PerformanceTestHelper {
    
    /**
     * 模拟单个录音文件的状态变化序列
     */
    suspend fun simulateTranscriptionProcess(
        viewModel: RecordingListViewModel,
        filePath: String
    ) {
        Timber.i("开始模拟录音处理流程: $filePath")
        
        // 重置性能统计
        PerformanceMonitor.resetStats()
        
        // 1. 上传中
        viewModel.updateProcessStatus(filePath, "UPLOADING")
        delay(1000)
        
        // 2. 转写中
        viewModel.updateProcessStatus(filePath, "TRANSCRIBING")
        delay(2000)
        
        // 3. 转写完成
        viewModel.updateProcessStatus(filePath, "TRANSCRIPTION_COMPLETED")
        delay(500)
        
        // 4. 总结中
        viewModel.updateProcessStatus(filePath, "SUMMARIZING")
        delay(1500)
        
        // 5. 总结完成
        viewModel.updateProcessStatus(filePath, "SUMMARY_COMPLETED")
        
        // 打印性能报告
        PerformanceMonitor.printPerformanceReport()
        
        Timber.i("录音处理流程模拟完成: $filePath")
    }
    
    /**
     * 模拟多个录音文件同时进行状态变化
     */
    suspend fun simulateBatchTranscriptionProcess(
        viewModel: RecordingListViewModel,
        filePaths: List<String>
    ) {
        Timber.i("开始模拟批量录音处理流程: ${filePaths.size} 个文件")
        
        // 重置性能统计
        PerformanceMonitor.resetStats()
        
        // 批量设置为上传中
        val uploadingUpdates = filePaths.associateWith { "UPLOADING" }
        viewModel.batchUpdateProcessStatus(uploadingUpdates)
        delay(1000)
        
        // 批量设置为转写中
        val transcribingUpdates = filePaths.associateWith { "TRANSCRIBING" }
        viewModel.batchUpdateProcessStatus(transcribingUpdates)
        delay(2000)
        
        // 模拟部分完成，部分失败的情况
        val mixedUpdates = mutableMapOf<String, String>()
        filePaths.forEachIndexed { index, filePath ->
            mixedUpdates[filePath] = if (index % 3 == 0) {
                "TRANSCRIPTION_FAILED"
            } else {
                "TRANSCRIPTION_COMPLETED"
            }
        }
        viewModel.batchUpdateProcessStatus(mixedUpdates)
        
        // 打印性能报告
        PerformanceMonitor.printPerformanceReport()
        
        Timber.i("批量录音处理流程模拟完成")
    }
    
    /**
     * 测试重复状态更新的性能
     */
    suspend fun testDuplicateStatusUpdates(
        viewModel: RecordingListViewModel,
        filePath: String,
        repeatCount: Int = 10
    ) {
        Timber.i("开始测试重复状态更新性能: $filePath, 重复次数: $repeatCount")
        
        // 重置性能统计
        PerformanceMonitor.resetStats()
        
        val startTime = System.currentTimeMillis()
        
        // 重复设置相同状态，应该被优化跳过
        repeat(repeatCount) {
            viewModel.updateProcessStatus(filePath, "TRANSCRIBING")
            delay(10)
        }
        
        val endTime = System.currentTimeMillis()
        val totalTime = endTime - startTime
        
        val stats = PerformanceMonitor.getPerformanceStats()
        
        Timber.i("""
            重复状态更新测试结果:
            - 总耗时: ${totalTime}ms
            - 实际状态更新次数: ${stats.statusUpdateCount}
            - 预期更新次数: 1 (首次更新)
            - 优化效果: ${if (stats.statusUpdateCount == 1) "成功" else "失败"}
        """.trimIndent())
        
        PerformanceMonitor.printPerformanceReport()
    }
    
    /**
     * 创建测试用的音频数据
     */
    fun createTestAudioData(count: Int): List<AudioTranscriptionBean> {
        return (1..count).map { index ->
            AudioTranscriptionBean(
                id = index,
                path = "/test/audio_$index.wav",
                transcriptionStatus = "NONE",
                summaryStatus = "NONE",
                isFirstShow = true
            )
        }
    }
    
    /**
     * 性能基准测试
     */
    suspend fun runPerformanceBenchmark(
        viewModel: RecordingListViewModel,
        audioCount: Int = 50
    ) {
        Timber.i("开始性能基准测试: $audioCount 个音频文件")
        
        val testAudios = createTestAudioData(audioCount)
        val filePaths = testAudios.map { it.path }
        
        // 测试1: 单个更新性能
        Timber.i("测试1: 单个状态更新性能")
        PerformanceMonitor.resetStats()
        val singleUpdateStartTime = System.currentTimeMillis()
        
        filePaths.forEach { filePath ->
            viewModel.updateProcessStatus(filePath, "TRANSCRIBING")
        }
        
        val singleUpdateEndTime = System.currentTimeMillis()
        val singleUpdateTime = singleUpdateEndTime - singleUpdateStartTime
        val singleUpdateStats = PerformanceMonitor.getPerformanceStats()
        
        // 测试2: 批量更新性能
        Timber.i("测试2: 批量状态更新性能")
        PerformanceMonitor.resetStats()
        val batchUpdateStartTime = System.currentTimeMillis()
        
        val batchUpdates = filePaths.associateWith { "TRANSCRIPTION_COMPLETED" }
        viewModel.batchUpdateProcessStatus(batchUpdates)
        
        val batchUpdateEndTime = System.currentTimeMillis()
        val batchUpdateTime = batchUpdateEndTime - batchUpdateStartTime
        val batchUpdateStats = PerformanceMonitor.getPerformanceStats()
        
        // 输出基准测试结果
        Timber.i("""
            ========== 性能基准测试结果 ==========
            音频文件数量: $audioCount
            
            单个更新测试:
            - 总耗时: ${singleUpdateTime}ms
            - 平均每次更新耗时: ${singleUpdateTime.toFloat() / audioCount}ms
            - 状态更新次数: ${singleUpdateStats.statusUpdateCount}
            
            批量更新测试:
            - 总耗时: ${batchUpdateTime}ms
            - 状态更新次数: ${batchUpdateStats.statusUpdateCount}
            - 批量更新次数: ${batchUpdateStats.batchUpdateCount}
            
            性能提升: ${if (batchUpdateTime < singleUpdateTime) "批量更新更快" else "单个更新更快"}
            时间差: ${kotlin.math.abs(singleUpdateTime - batchUpdateTime)}ms
            =====================================
        """.trimIndent())
    }
}
